/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}



/* Header Styles */
.header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    background: #dc2626;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.2rem;
}

.logo-text h1 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 2px;
}

.status {
    font-size: 0.8rem;
    color: #6b7280;
    display: flex;
    align-items: center;
    gap: 4px;
}

.status i {
    color: #10b981;
    font-size: 0.6rem;
}

.nav {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 1.5rem;
}

.nav-list a {
    text-decoration: none;
    color: #6b7280;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: color 0.3s;
}

.nav-list a:hover {
    color: #3b82f6;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    color: #6b7280;
    font-size: 1.1rem;
    transition: color 0.3s;
}

.social-links a:hover {
    color: #3b82f6;
}

.contact-btn {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: background 0.3s;
}

.contact-btn:hover {
    background: #2563eb;
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
}

.mobile-menu-toggle span {
    width: 24px;
    height: 3px;
    background: #374151;
    margin: 2px 0;
    transition: 0.3s;
}

/* Mobile Menu */
.mobile-menu {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0,0,0,0.5);
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.mobile-menu.active {
    display: block;
    opacity: 1;
    visibility: visible;
}

.mobile-menu-content {
    background: white;
    width: 280px;
    height: 100%;
    padding: 2rem;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.mobile-menu.active .mobile-menu-content {
    transform: translateX(0);
}

.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e5e7eb;
}

.mobile-menu-header h3 {
    color: #1f2937;
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
}

.mobile-menu-close {
    background: none;
    border: none;
    color: #6b7280;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.mobile-menu-close:hover {
    color: #dc2626;
    background-color: #fef2f2;
}

.mobile-menu-content a {
    display: block;
    padding: 1rem 0;
    text-decoration: none;
    color: #374151;
    font-weight: 500;
    border-bottom: 1px solid #e5e7eb;
    transition: color 0.3s ease, background-color 0.3s ease;
    border-radius: 4px;
    margin-bottom: 4px;
}

.mobile-menu-content a:hover {
    color: #3b82f6;
    background-color: #f8fafc;
    padding-left: 1rem;
}

.mobile-menu-content a i {
    margin-right: 8px;
    width: 20px;
    text-align: center;
}

.mobile-social {
    margin-top: 2rem;
    display: flex;
    gap: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

.mobile-social a {
    color: #6b7280;
    font-size: 1.2rem;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
    border-bottom: none !important;
}

.mobile-social a:hover {
    color: #3b82f6;
    background-color: #f0f9ff;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    padding: 4rem 0;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.hero-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-features {
    margin-bottom: 2rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 0.5rem;
}

.feature i {
    color: #10b981;
}

.search-section {
    margin-top: 2rem;
}

.search-box {
    background: white;
    border-radius: 12px;
    padding: 8px;
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 1rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.search-box i {
    color: #6b7280;
    margin-left: 8px;
}

.search-box input {
    flex: 1;
    border: none;
    outline: none;
    padding: 0.5rem;
    font-size: 1rem;
    color: #374151;
}

.search-btn {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background 0.3s;
}

.search-btn:hover {
    background: #2563eb;
}

.location-btn {
    background: rgba(255,255,255,0.1);
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s;
}

.location-btn:hover {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.5);
}

/* Hero Visual */
.hero-visual {
    display: flex;
    justify-content: center;
}

.map-preview {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
    width: 100%;
    max-width: 400px;
}

.map-header {
    background: #f8fafc;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e2e8f0;
}

.map-header h3 {
    color: #1f2937;
    font-size: 0.9rem;
    font-weight: 600;
}

.map-controls {
    display: flex;
    gap: 4px;
}

.map-controls button {
    background: #e2e8f0;
    border: none;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    color: #64748b;
}

.map-content {
    height: 300px;
    background: linear-gradient(45deg, #e2e8f0 25%, transparent 25%),
                linear-gradient(-45deg, #e2e8f0 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #e2e8f0 75%),
                linear-gradient(-45deg, transparent 75%, #e2e8f0 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    position: relative;
    overflow: hidden;
}

.map-markers {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.marker {
    position: absolute;
    width: 12px;
    height: 12px;
    background: #dc2626;
    border: 2px solid white;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.marker-1 { top: 30%; left: 25%; }
.marker-2 { top: 60%; left: 70%; }
.marker-3 { top: 45%; left: 45%; }
.marker-4 { top: 75%; left: 30%; }

.map-info {
    position: absolute;
    bottom: 1rem;
    left: 1rem;
    right: 1rem;
}

.info-card {
    background: white;
    padding: 0.75rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    font-size: 0.8rem;
}

.info-card h4 {
    color: #1f2937;
    font-weight: 600;
    margin-bottom: 4px;
}

.info-card p {
    color: #6b7280;
    margin-bottom: 2px;
}

/* Ad Banner */
.ad-banner {
    background: #f8fafc;
    padding: 1rem 0;
    border-top: 1px solid #e2e8f0;
    border-bottom: 1px solid #e2e8f0;
}

.ad-placeholder {
    background: #e2e8f0;
    border: 2px dashed #94a3b8;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 90px;
}

.ad-placeholder.vertical {
    min-height: 600px;
    width: 300px;
}

.ad-content {
    color: #64748b;
    font-weight: 500;
    text-align: center;
}

/* Stats Section */
.stats {
    background: #f8fafc;
    padding: 3rem 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    text-align: center;
}

.stat-item {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #3b82f6;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.stat-desc {
    color: #6b7280;
    font-size: 0.9rem;
}

/* Features Section */
.features {
    padding: 3rem 0;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    text-align: center;
}

.feature-item {
    padding: 1.5rem;
}

.feature-item i {
    font-size: 2rem;
    color: #3b82f6;
    margin-bottom: 1rem;
}

.feature-item h3 {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
}

/* Main Content */
.main-content {
    padding: 4rem 0;
    background: #f8fafc;
}

.content-grid {
    display: grid;
    grid-template-columns: 2fr 300px;
    gap: 3rem;
}

.content-section {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    margin-bottom: 2rem;
}

.section-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 1rem;
}

.section-header i {
    color: #3b82f6;
    font-size: 1.5rem;
}

.section-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
}

.content-section p {
    color: #6b7280;
    margin-bottom: 1.5rem;
}

/* Pharmacy List */
.pharmacy-list {
    margin-bottom: 1.5rem;
}

.pharmacy-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-bottom: 1rem;
    transition: border-color 0.3s;
}

.pharmacy-item:hover {
    border-color: #3b82f6;
}

.pharmacy-info h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.pharmacy-info p {
    color: #6b7280;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
    gap: 6px;
}

.pharmacy-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-directions, .btn-call {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s;
    font-size: 0.9rem;
}

.btn-directions {
    background: #3b82f6;
    color: white;
}

.btn-directions:hover {
    background: #2563eb;
}

.btn-call {
    background: #10b981;
    color: white;
}

.btn-call:hover {
    background: #059669;
}

/* Map Container */
.map-container {
    margin-bottom: 1.5rem;
}

.interactive-map {
    height: 400px;
    background: #f1f5f9;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e2e8f0;
}

.map-placeholder {
    text-align: center;
    color: #64748b;
}

.map-placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

/* Districts Grid */
.districts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.district-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 1rem;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    text-decoration: none;
    color: #374151;
    transition: all 0.3s;
}

.district-item:hover {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.district-item i {
    color: #3b82f6;
    transition: color 0.3s;
}

.district-item:hover i {
    color: white;
}

.district-item span {
    font-weight: 600;
    flex: 1;
}

.district-item small {
    color: #6b7280;
    transition: color 0.3s;
}

.district-item:hover small {
    color: rgba(255,255,255,0.8);
}

/* View More Links */
.view-more {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    color: #3b82f6;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s;
}

.view-more:hover {
    color: #2563eb;
}

/* Sidebar Ad */
.sidebar-ad {
    position: sticky;
    top: 100px;
    height: fit-content;
}

/* Footer */
.footer {
    background: #1f2937;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3, .footer-section h4 {
    margin-bottom: 1rem;
    font-weight: 600;
}

.footer-section p {
    color: #9ca3af;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #9ca3af;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-section ul li a:hover {
    color: white;
}

.footer-section .social-links {
    display: flex;
    gap: 1rem;
}

.footer-section .social-links a {
    color: #9ca3af;
    font-size: 1.2rem;
    transition: color 0.3s;
}

.footer-section .social-links a:hover {
    color: #3b82f6;
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 1rem;
    text-align: center;
    color: #9ca3af;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .content-grid {
        grid-template-columns: 1fr;
    }

    .sidebar-ad {
        order: -1;
        position: static;
    }

    .ad-placeholder.vertical {
        width: 100%;
        min-height: 250px;
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }
}

@media (max-width: 768px) {
    .nav {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    /* Ensure mobile menu is properly displayed on mobile */
    .mobile-menu {
        display: none;
    }

    .mobile-menu.active {
        display: block !important;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-text h2 {
        font-size: 2rem;
    }

    .search-box {
        flex-direction: column;
        align-items: stretch;
    }

    .search-btn {
        justify-content: center;
        margin-top: 0.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .pharmacy-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .pharmacy-actions {
        width: 100%;
        justify-content: space-between;
    }

    .districts-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .container {
        padding: 0 15px;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 2rem 0;
    }

    .hero-text h2 {
        font-size: 1.5rem;
    }

    .content-section {
        padding: 1.5rem;
    }

    .stat-item {
        padding: 1.5rem;
    }

    .stat-number {
        font-size: 2rem;
    }
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    transform: translateX(-100%);
    animation: slideIn 0.5s ease forwards;
}

@keyframes slideIn {
    to {
        transform: translateX(0);
    }
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Focus States for Accessibility */
button:focus,
input:focus,
a:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .header,
    .ad-banner,
    .sidebar-ad,
    .footer {
        display: none;
    }

    .hero {
        background: white;
        color: black;
    }

    .content-section {
        box-shadow: none;
        border: 1px solid #ccc;
    }
}

